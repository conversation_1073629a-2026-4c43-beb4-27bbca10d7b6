# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm start` - Start the Electron application in development mode
- `npm run package` - Package the application for distribution
- `npm run make` - Create distribution packages (Windows, macOS, Linux)
- `npm run publish` - Publish the application
- `npm run release` - Bump version and create git release tag

Note: There is no linting configured (`lint` command outputs "No linting configured")

## Architecture Overview

This is an Electron desktop application for smart building automation and control, built with React and Vite. It manages IoT devices, lighting, HVAC, and building automation systems.

### Core Architecture

**Main Process (Electron)**
- `src/main.js` - Main Electron process entry point
- `src/preload.js` - Preload script for secure renderer-main communication
- `src/services/database.js` - SQLite database service using better-sqlite3
- `src/services/udp.js` - UDP communication for device networking

**Renderer Process (React App)**
- `src/app.jsx` - Main React application with sidebar layout
- `src/renderer.js` - Renderer process entry point
- Context-based state management via `src/contexts/`

### Key Services

**RCU Controller (`src/services/rcu-controller/`)**
The core of the application - handles all IoT device communication:
- `lighting.js` - Lighting system control (groups, outputs, states)
- `air-conditioner.js` - HVAC control (temperature, modes, fan settings)
- `scene.js` / `schedule.js` - Automation scenarios and timing
- `multi-scene.js` / `sequence.js` - Complex automation workflows
- `knx.js` - KNX protocol integration
- `curtain.js` - Motorized curtain control
- `rs485.js` - RS485 serial communication
- `input-config.js` / `output.js` - I/O configuration
- `firmware.js` - Device firmware updates

**Database Structure**
- SQLite database stored in `~/Documents/Toolkit Engine/projects.db`
- Project-based organization with support for export/import
- Handles units, scenes, schedules, sequences, and device configurations

### UI Architecture

**Component Structure**
- `src/components/ui/` - Reusable UI components (shadcn/ui based)
- `src/components/projects/` - Project management components
- `src/components/shared/` - Shared business logic components
- Data tables use TanStack Table with custom editable cells

**Key Features**
- Project-based organization with import/export capabilities
- Real-time device monitoring and control
- Tabbed interface for different system types (lighting, HVAC, scenes)
- Network unit management with bulk configuration

### Device Communication

The application communicates with IoT devices through:
- UDP networking for device discovery and control
- RS485 serial communication for legacy devices
- KNX protocol support for building automation
- Custom RCU (Remote Control Unit) protocol

### Build Configuration

- Uses Electron Forge for packaging and distribution
- Vite for fast development and building
- Tailwind CSS v4 for styling
- Supports Windows (Squirrel), macOS (ZIP), and Linux (DEB/RPM) distributions

### Project Context System

The application uses React Context for project state management:
- `ProjectProvider` - Manages project CRUD operations
- `ProjectDetailProvider` - Handles project detail views and navigation
- Database operations are exposed through `window.electronAPI` from preload script
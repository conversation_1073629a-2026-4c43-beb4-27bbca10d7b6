<p align="center">
    <img style="width:60px;" src="./assets/app.png" />
</p>

<h2 align="center"> Toolkit Engine </h2>

<h4 align="center"> Professional automation and control toolkit for GNT smart IoT system.</h4>

<p align="center">
    <img src="https://img.shields.io/badge/license-MIT-blue.svg"/>
    <img src="https://img.shields.io/badge/PRs-welcome-brightgreen.svg"/>
</p>

## Overview

GNT Toolkit Engine is a comprehensive desktop application designed for managing and controlling smart building systems. It provides an intuitive interface for building automation professionals to configure, monitor, and control various building systems including lighting, HVAC, and scheduling.

## Key Features

- **Project Management**: Create and manage multiple smart building projects
- **Group Configuration**: Configure and control device groups across different systems
- **Scene & Schedule Management**: Set up automated scenarios and time-based schedules
- **HVAC Control**: Advanced air conditioning system management with temperature, fan speed, and mode controls
- **Lighting Control**: Comprehensive lighting system management and automation
- **Real-time Monitoring**: Live status monitoring and control of all connected systems
- **User-friendly Interface**: Modern, intuitive interface designed for professional use

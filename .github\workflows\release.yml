name: Build and Release

on:
  push:
    tags:
      - "v*"
jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install dependencies
        run: npm install

      - name: Build Electron App
        run: npm run make

      - name: Upload release assets
        uses: softprops/action-gh-release@v2
        with:
          files: |
            out/make/**/*.exe
            out/make/**/*.zip
            out/make/**/*.deb
            out/make/**/*.rpm
            out/make/**/*.nupkg
            out/make/**/RELEASES
        env:
          GITHUB_TOKEN: ${{ secrets.GNT_ENGINE }}
